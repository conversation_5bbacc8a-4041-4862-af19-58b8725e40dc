import { TokenManager, authService } from '@/lib/auth-utils';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock axios
jest.mock('axios', () => ({
  post: jest.fn(),
  get: jest.fn(),
  interceptors: {
    request: { use: jest.fn() },
    response: { use: jest.fn() },
  },
  isAxiosError: jest.fn(),
}));

describe('TokenManager', () => {
  let tokenManager: TokenManager;

  beforeEach(() => {
    tokenManager = TokenManager.getInstance();
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
  });

  describe('setTokens and getTokens', () => {
    it('should store and retrieve tokens', () => {
      const tokens = {
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() / 1000 + 3600,
      };

      tokenManager.setTokens(tokens);

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'auth_tokens',
        JSON.stringify(tokens)
      );
    });

    it('should return null when no tokens stored', () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      const tokens = tokenManager.getTokens();
      
      expect(tokens).toBeNull();
    });

    it('should return null when stored data is invalid JSON', () => {
      localStorageMock.getItem.mockReturnValue('invalid-json');
      
      const tokens = tokenManager.getTokens();
      
      expect(tokens).toBeNull();
    });
  });

  describe('needsRefresh', () => {
    it('should return true when token expires soon', () => {
      const tokens = {
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() / 1000 + 60, // expires in 1 minute
      };

      localStorageMock.getItem.mockReturnValue(JSON.stringify(tokens));
      
      const needsRefresh = tokenManager.needsRefresh();
      
      expect(needsRefresh).toBe(true);
    });

    it('should return false when token has plenty of time', () => {
      const tokens = {
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() / 1000 + 3600, // expires in 1 hour
      };

      localStorageMock.getItem.mockReturnValue(JSON.stringify(tokens));
      
      const needsRefresh = tokenManager.needsRefresh();
      
      expect(needsRefresh).toBe(false);
    });
  });

  describe('isExpired', () => {
    it('should return true when token is expired', () => {
      const tokens = {
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() / 1000 - 60, // expired 1 minute ago
      };

      localStorageMock.getItem.mockReturnValue(JSON.stringify(tokens));
      
      const isExpired = tokenManager.isExpired();
      
      expect(isExpired).toBe(true);
    });

    it('should return false when token is still valid', () => {
      const tokens = {
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() / 1000 + 3600, // expires in 1 hour
      };

      localStorageMock.getItem.mockReturnValue(JSON.stringify(tokens));
      
      const isExpired = tokenManager.isExpired();
      
      expect(isExpired).toBe(false);
    });
  });

  describe('clearTokens', () => {
    it('should remove tokens from storage', () => {
      tokenManager.clearTokens();
      
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_tokens');
    });
  });
});

describe('authService', () => {
  describe('isAuthenticated', () => {
    it('should return false when no tokens', () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      const isAuthenticated = authService.isAuthenticated();
      
      expect(isAuthenticated).toBe(false);
    });

    it('should return false when tokens are expired', () => {
      const tokens = {
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() / 1000 - 60, // expired
      };

      localStorageMock.getItem.mockReturnValue(JSON.stringify(tokens));
      
      const isAuthenticated = authService.isAuthenticated();
      
      expect(isAuthenticated).toBe(false);
    });

    it('should return true when tokens are valid', () => {
      const tokens = {
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() / 1000 + 3600, // valid for 1 hour
      };

      localStorageMock.getItem.mockReturnValue(JSON.stringify(tokens));
      
      const isAuthenticated = authService.isAuthenticated();
      
      expect(isAuthenticated).toBe(true);
    });
  });

  describe('getCurrentUser', () => {
    it('should return null when no tokens', () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      const user = authService.getCurrentUser();
      
      expect(user).toBeNull();
    });

    it('should decode user from valid JWT token', () => {
      // Mock JWT token with base64 encoded payload
      const payload = {
        id: 'user-id',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phoneNumber: '+1234567890',
      };
      
      const encodedPayload = btoa(JSON.stringify(payload));
      const mockToken = `header.${encodedPayload}.signature`;
      
      const tokens = {
        accessToken: mockToken,
        refreshToken: 'refresh-token',
        expiresAt: Date.now() / 1000 + 3600,
      };

      localStorageMock.getItem.mockReturnValue(JSON.stringify(tokens));
      
      const user = authService.getCurrentUser();
      
      expect(user).toEqual(payload);
    });
  });
});
