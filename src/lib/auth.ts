import type { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import axios from "axios";

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  accessToken: string;
  refreshToken: string;
}

interface APIResponse {
  status: boolean;
  message: string;
  data: {
    accessToken: string;
    refreshToken: string;
    user: User;
    expiresAt: number;
  };
}

// Helper function to fetch profile from API
async function fetchUserProfile(accessToken: string) {
  try {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/auth/profile`, {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });

    if (response.data.status) {
      return response.data.data;
    }
    return null;
  } catch (error) {
    console.error('Failed to fetch user profile:', error);
    return null;
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required");
        }

        try {
          const { data } = await axios.post<APIResponse>(
            `${process.env.NEXT_PUBLIC_API_URL}/auth/login`,
            {
              emailPhoneNumber: credentials.email,
              password: credentials.password,
            }
          );

          if (!data.status || !data.data) {
            throw new Error(data.message || "Invalid credentials");
          }

          return {
            id: data.data.user.id,
            name: `${data.data.user.firstName} ${data.data.user.lastName}`,
            email: data.data.user.email,
            firstName: data.data.user.firstName,
            lastName: data.data.user.lastName,
            phoneNumber: data.data.user.phoneNumber,
            accessToken: data.data.accessToken,
            refreshToken: data.data.refreshToken,
          };
        } catch (error) {
          if (axios.isAxiosError(error)) {
            const message = error.response?.data?.message || "Authentication failed";
            throw new Error(message);
          }
          throw new Error("Authentication failed");
        }
      },
    }),
  ],
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
    maxAge: 15 * 60, // 15 minutes
  },
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async jwt({ token, user, account }) {
      // Initial sign in
      if (user && account) {
        if (account.provider === "google") {
          // Handle Google OAuth
          try {
            const { data } = await axios.post<APIResponse>(
              `${process.env.NEXT_PUBLIC_API_URL}/auth/google`,
              {
                googleToken: account.id_token,
              }
            );

            if (data.status) {
              return {
                ...token,
                id: data.data.user.id,
                firstName: data.data.user.firstName,
                lastName: data.data.user.lastName,
                phoneNumber: data.data.user.phoneNumber,
                accessToken: data.data.accessToken,
                refreshToken: data.data.refreshToken,
                expiresAt: data.data.expiresAt,
              };
            }
          } catch (error) {
            console.error("Google auth failed:", error);
            return token;
          }
        } else {
          // Handle credentials login
          return {
            ...token,
            id: (user as any).id,
            firstName: (user as any).firstName,
            lastName: (user as any).lastName,
            phoneNumber: (user as any).phoneNumber,
            accessToken: (user as any).accessToken,
            refreshToken: (user as any).refreshToken,
          };
        }
      }

      return token;
    },
    async session({ session, token }) {
      // Fetch fresh profile data from API
      if (token.accessToken) {
        try {
          const profile = await fetchUserProfile(token.accessToken as string);
          if (profile) {
            session.user = {
              ...session.user,
              id: profile.id,
              firstName: profile.firstName,
              lastName: profile.lastName,
              phoneNumber: profile.phoneNumber,
              name: `${profile.firstName} ${profile.lastName}`,
            };
            (session as any).accessToken = token.accessToken;
            (session as any).refreshToken = token.refreshToken;
          }
        } catch (error) {
          console.error("Failed to fetch profile:", error);
          // Fallback to token data
          session.user = {
            ...session.user,
            id: token.id as string,
            firstName: token.firstName as string,
            lastName: token.lastName as string,
            phoneNumber: token.phoneNumber as string,
            name: `${token.firstName} ${token.lastName}`,
          };
        }
      }

      return session;
    },
  },
};