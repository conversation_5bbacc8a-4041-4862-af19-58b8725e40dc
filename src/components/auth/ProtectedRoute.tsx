'use client';

import { ReactNode } from 'react';
import { useRequireAuth } from '@/hooks/useAuth';
import { Box, Spinner, Center } from '@chakra-ui/react';

interface ProtectedRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export function ProtectedRoute({ children, fallback }: ProtectedRouteProps) {
  const { isAuthenticated, isLoading } = useRequireAuth();

  if (isLoading) {
    return fallback || (
      <Center h="100vh">
        <Box textAlign="center">
          <Spinner size="xl" color="blue.500" thickness="4px" />
          <Box mt={4} fontSize="lg" color="gray.600">
            Loading...
          </Box>
        </Box>
      </Center>
    );
  }

  if (!isAuthenticated) {
    return null; // useRequireAuth will handle redirect
  }

  return <>{children}</>;
}
