// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/client"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(uuid())
  userId        String?   @unique
  firstName     String?
  lastName      String?
  email         String?   @unique
  emailVerified DateTime?
  phoneNumber   String?   @unique
  phoneVerified DateTime?
  role          String   @default("user")
  oauthProvider String?
  oauthId       String?
  googleId      String?   @unique
  password      String?
  refreshToken  String?   @db.Text
  image         String?
  isActive      Boolean  @default(true)
  isEmailVerified Boolean @default(false)
  isPhoneVerified Boolean @default(false)
  isBlocked     Boolean  @default(false)
  country       String?
  ipAddress     String?
  lastLogin     DateTime?
  lastLoginIp   String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}