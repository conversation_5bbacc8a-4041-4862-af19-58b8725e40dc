# Authentication System Setup

## Overview

This project implements a secure, modern authentication system with the following features:

- **NextAuth.js** for frontend authentication
- **JWT tokens** with refresh token mechanism
- **Google OAuth** integration
- **Profile API** for session management
- **Rate limiting** and security middleware
- **OWASP security compliance**

## Architecture

### Backend (Hono.js + Prisma)
- JWT-based authentication with access/refresh tokens
- Google OAuth token verification
- Rate limiting middleware
- Secure password hashing with bcrypt
- Profile API endpoint

### Frontend (Next.js + NextAuth)
- NextAuth.js for session management
- Custom auth hooks and utilities
- Protected route middleware
- Automatic token refresh
- Google OAuth integration

## Setup Instructions

### 1. Environment Variables

Copy `.env.example` to `.env.local` and configure:

```bash
cp .env.example .env.local
```

Required variables:
- `DATABASE_URL`: MySQL database connection
- `NEXTAUTH_SECRET`: Secret for NextAuth.js
- `JWT_SECRET`: Secret for JWT tokens (different from NEXTAUTH_SECRET)
- `GOOGLE_CLIENT_ID`: Google OAuth client ID
- `GOOGLE_CLIENT_SECRET`: Google OAuth client secret
- `NEXT_PUBLIC_API_URL`: Backend API URL

### 2. Database Setup

Run Prisma migrations:

```bash
npx prisma migrate dev
npx prisma generate
```

### 3. Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google` (development)
   - `https://yourdomain.com/api/auth/callback/google` (production)

## Usage

### Frontend Authentication

```tsx
import { useAuth } from '@/hooks/useAuth';

function LoginPage() {
  const { login, loginWithGoogle, isLoading, error } = useAuth();

  const handleLogin = async (email: string, password: string) => {
    const result = await login(email, password);
    if (result.success) {
      // Redirect to dashboard
    }
  };

  return (
    <LoginForm 
      onLogin={handleLogin}
      onGoogleLogin={loginWithGoogle}
      isLoading={isLoading}
      error={error}
    />
  );
}
```

### Protected Routes

```tsx
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

function Dashboard() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  );
}
```

### API Calls with Authentication

```tsx
import { authService } from '@/lib/auth-utils';

// Automatically handles token refresh
const profile = await authService.getProfile();
```

## Security Features

### 1. Token Management
- **Access tokens**: 15-minute expiry
- **Refresh tokens**: 7-day expiry
- **Automatic refresh**: Before token expiry
- **Secure storage**: HttpOnly cookies (recommended) or localStorage

### 2. Rate Limiting
- **Auth endpoints**: 50 requests per 15 minutes
- **IP-based tracking**: Prevents brute force attacks
- **Configurable limits**: Per endpoint customization

### 3. Security Headers
- **X-Frame-Options**: Prevents clickjacking
- **X-Content-Type-Options**: Prevents MIME sniffing
- **CSP**: Content Security Policy
- **XSS Protection**: Cross-site scripting prevention

### 4. Input Validation
- **Zod schemas**: Type-safe validation
- **Password requirements**: Minimum 8 characters
- **Email validation**: RFC compliant
- **Phone number validation**: International format

## API Endpoints

### Authentication
- `POST /auth/login` - Email/password login
- `POST /auth/register` - User registration
- `POST /auth/google` - Google OAuth login
- `POST /auth/refresh-token` - Token refresh
- `GET /auth/profile` - Get user profile (protected)

### Request/Response Examples

#### Login
```bash
POST /auth/login
{
  "emailPhoneNumber": "<EMAIL>",
  "password": "password123"
}

Response:
{
  "status": true,
  "message": "Login successful",
  "data": {
    "accessToken": "eyJ...",
    "refreshToken": "eyJ...",
    "user": {
      "id": "user-id",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phoneNumber": "+1234567890"
    },
    "expiresAt": 1640995200
  }
}
```

## Troubleshooting

### Common Issues

1. **Google OAuth not working**
   - Check redirect URIs in Google Console
   - Verify GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET
   - Ensure domain is authorized

2. **Token refresh failing**
   - Check JWT_SECRET configuration
   - Verify refresh token in database
   - Check token expiry times

3. **Database connection issues**
   - Verify DATABASE_URL format
   - Check database server status
   - Run `npx prisma db push` to sync schema

### Debug Mode

Enable debug logging:

```bash
DEBUG=nextauth* npm run dev
```

## Production Deployment

### Security Checklist

- [ ] Use strong, unique secrets for JWT_SECRET and NEXTAUTH_SECRET
- [ ] Enable HTTPS in production
- [ ] Configure proper CORS settings
- [ ] Set up Redis for rate limiting
- [ ] Enable database SSL
- [ ] Configure proper CSP headers
- [ ] Set up monitoring and logging
- [ ] Regular security updates

### Environment Variables

Ensure all production environment variables are set:
- Use secure random secrets
- Configure production database
- Set NEXTAUTH_URL to production domain
- Update Google OAuth redirect URIs

## Contributing

When contributing to the auth system:

1. Follow security best practices
2. Add tests for new features
3. Update documentation
4. Review security implications
5. Test with different browsers and devices
