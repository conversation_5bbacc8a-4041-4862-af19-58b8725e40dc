{"name": "king-collectibles-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@chakra-ui/cli": "^3.16.1", "@chakra-ui/react": "^3.16.1", "@emotion/react": "^11.14.0", "@fontsource/poppins": "^5.2.6", "@hono/swagger-ui": "0.5.0", "@hono/zod-openapi": "^0.19.6", "@prisma/client": "^6.7.0", "@tanstack/react-query": "^5.76.1", "@types/negotiator": "^0.6.3", "add": "^2.0.6", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "google-auth-library": "^9.15.1", "hono": "^4.7.9", "negotiator": "^1.0.0", "next": "15.3.1", "next-auth": "^4.24.11", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "prisma": "^6.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-select": "^5.10.1", "snippet": "^0.1.0", "swiper": "^11.2.6", "use-mask-input": "^3.4.2", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/bcryptjs": "^3.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "typescript": "^5"}}